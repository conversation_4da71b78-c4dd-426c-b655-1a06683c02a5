-- Make work_package_id NOT NULL in purchase_order table
-- This enforces the one-to-many relationship where every purchase order must be associated with a work package
-- First, check if there are any existing records with NULL work_package_id
-- If there are any, this migration will fail and require data cleanup first
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM public.purchase_order WHERE work_package_id IS NULL) THEN
        RAISE EXCEPTION 'Cannot make work_package_id NOT NULL: found % records with NULL work_package_id. Please assign work packages to all purchase orders before running this migration.',
            (SELECT COUNT(*) FROM public.purchase_order WHERE work_package_id IS NULL);
    END IF;
END $$;

-- Add NOT NULL constraint to work_package_id column
ALTER TABLE public.purchase_order
ALTER COLUMN work_package_id
SET NOT NULL;

-- Update the comment to reflect the new constraint
COMMENT ON COLUMN public.purchase_order.work_package_id IS 'Associated work package for this purchase order (required)';
