import { faker, fakerSV } from '@faker-js/faker';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../database.types';
import {
	StandardRibaStages,
	upsertBudgetLineItem,
	createBudgetSnapshot,
	completeProjectStage,
} from '../project_utils';
import { capitalizeFirstLetter } from '$lib/utils';

export async function generateRibaDemoProjectData(
	supabase: SupabaseClient<Database>,
	totalBudget: number = Math.round(1_000_000_000 * (0.9 + Math.random() * 0.2)),
) {
	// Lookup the ICMS v3 WBS library id
	const { data: library } = await supabase
		.from('wbs_library')
		.select('wbs_library_id')
		.eq('name', 'ICMS v3')
		.maybeSingle();
	if (!library) throw new Error('ICMS v3 library not found');

	// Get org_id
	const { data: org } = await supabase
		.from('organization')
		.select('org_id')
		.eq('name', 'Aurora')
		.maybeSingle();
	if (!org) throw new Error('Aurora org not found');

	// Create a demo client
	const clientName = `${capitalizeFirstLetter(faker.color.human())} ${capitalizeFirstLetter(faker.science.chemicalElement().name)}`;
	const { error: clientError } = await supabase.from('client').insert({
		name: clientName,
		description: faker.company.catchPhrase(),
		org_id: org.org_id,
	});

	if (clientError) throw new Error('Client insert failed');
	const { data: client } = await supabase
		.from('client')
		.select('client_id')
		.eq('name', clientName)
		.maybeSingle();
	if (!client) throw new Error('No client found');

	// Create project
	const projectName = fakerSV.location.streetAddress();
	const { error: projectError } = await supabase.from('project').insert({
		name: projectName,
		description: faker.company.catchPhrase(),
		client_id: client.client_id,
		wbs_library_id: library.wbs_library_id,
	});
	if (projectError) throw new Error('Project insert failed');

	const { data: project } = await supabase
		.from('project')
		.select('project_id')
		.eq('name', projectName)
		.maybeSingle();
	if (!project) throw new Error('No project found');
	const projectId = project.project_id;

	// Create stages 0-5
	const stageIds: string[] = [];
	for (let i = 0; i <= 5; i++) {
		const stage = StandardRibaStages[i];
		const { data: s } = await supabase
			.from('project_stage')
			.insert({
				project_id: projectId,
				name: stage.name,
				stage_order: stage.stage_order,
				stage: stage.stage,
			})
			.select('project_stage_id')
			.single();
		if (!s) throw new Error('No stage found');
		stageIds.push(s.project_stage_id);
	}

	// Helper to fetch WBS items
	const getItems = async (level: number, parent?: string) => {
		const query = supabase
			.from('wbs_library_item')
			.select('wbs_library_item_id, code')
			.eq('wbs_library_id', library.wbs_library_id)
			.eq('level', level);
		if (parent) query.eq('parent_item_id', parent);
		const { data } = await query;
		return data || [];
	};

	// Stage 1 - level 2 costs
	const level2 = await getItems(2);
	const acq = level2.find((i) => i.code === '01.01');
	const constr = level2.find((i) => i.code === '01.02');
	if (!acq || !constr) throw new Error('ICMS level 2 codes missing');

	const acqTotal = totalBudget * 0.3;
	const constrTotal = totalBudget * 0.7;

	await upsertBudgetLineItem(supabase, {
		project_id: projectId,
		wbs_library_item_id: acq.wbs_library_item_id,
		quantity: 1,
		material_rate: acqTotal,
		unit_rate: acqTotal,
		unit_rate_manual_override: false,
	});

	await upsertBudgetLineItem(supabase, {
		project_id: projectId,
		wbs_library_item_id: constr.wbs_library_item_id,
		quantity: 1,
		material_rate: constrTotal,
		unit_rate: constrTotal,
		unit_rate_manual_override: false,
	});

	let snapshotId = await createBudgetSnapshot(supabase, stageIds[0], 'Stage 1 budget');

	// Utility to spread costs to child level
	const spreadToChildren = async (snapshot: string, level: number, variance: number) => {
		const { data: lines } = await supabase
			.from('budget_snapshot_line_item')
			.select('wbs_library_item_id, unit_rate')
			.eq('budget_snapshot_id', snapshot);

		await supabase.from('budget_line_item_current').delete().eq('project_id', projectId);

		for (const line of lines || []) {
			const children = await getItems(level, line.wbs_library_item_id);
			const stageTotal = (line.unit_rate || 0) * (1 + (Math.random() * 2 - 1) * variance);
			if (!children.length) {
				await upsertBudgetLineItem(supabase, {
					project_id: projectId,
					wbs_library_item_id: line.wbs_library_item_id,
					quantity: 1,
					material_rate: stageTotal,
					unit_rate: stageTotal,
					unit_rate_manual_override: false,
				});
			} else {
				const base = stageTotal / children.length;
				let remaining = stageTotal;
				let idx = 0;
				for (const child of children) {
					idx++;
					const portion = idx < children.length ? base * (0.75 + Math.random() * 0.5) : remaining;
					remaining -= portion;
					await upsertBudgetLineItem(supabase, {
						project_id: projectId,
						wbs_library_item_id: child.wbs_library_item_id,
						quantity: 1,
						material_rate: portion,
						unit_rate: portion,
						unit_rate_manual_override: false,
					});
				}
			}
		}
	};

	// Stage 2 -> level 3 with +/-25%
	await spreadToChildren(snapshotId, 3, 0.25);
	snapshotId = await createBudgetSnapshot(supabase, stageIds[1], 'Stage 2 budget');

	// Stage 3 -> level 4 with +/-15%
	await spreadToChildren(snapshotId, 4, 0.15);
	snapshotId = await createBudgetSnapshot(supabase, stageIds[2], 'Stage 3 budget');

	// Stage 4 -> adjust level 4 +/-10%
	await spreadToChildren(snapshotId, 4, 0.1);
	snapshotId = await createBudgetSnapshot(supabase, stageIds[3], 'Stage 4 budget');

	// Stage 5 -> adjust +/-5%
	await spreadToChildren(snapshotId, 4, 0.05);
	snapshotId = await createBudgetSnapshot(supabase, stageIds[4], 'Stage 5 budget');

	// Stage 6 -> final adjust +/-5%
	await spreadToChildren(snapshotId, 5, 0.05);

	// Complete early stages
	for (let i = 0; i < 5; i++) {
		await completeProjectStage(supabase, stageIds[i]);
	}

	// Vendors
	const vendors: string[] = [];
	for (let i = 0; i < 30; i++) {
		const { data: v } = await supabase
			.from('vendor')
			.insert({
				name: `${capitalizeFirstLetter(faker.helpers.arrayElement([faker.animal.cow(), faker.animal.bird(), faker.animal.rabbit()])).replace(' cattle', '')} ${faker.helpers.arrayElement(
					['Contractor', 'Supplier', 'Consultants', 'Equipment Rental', 'Professional Services'],
				)}`,
				description: faker.company.catchPhrase(),
				project_id: projectId,
			})
			.select('vendor_id')
			.single();
		if (!v) throw new Error('Vendor insert & select failed');
		vendors.push(v.vendor_id);
	}

	// Get level 4 WBS items (leaf nodes) under construction for work packages
	const level4Items = await getItems(4);
	const constructionLevel4Items = level4Items.filter((item) => item.code.startsWith('01.02'));

	if (constructionLevel4Items.length === 0) {
		throw new Error('No level 4 construction WBS items found for work packages');
	}

	// Work packages and purchase orders
	const poIds: string[] = [];
	for (const vendorId of vendors) {
		// Randomly select a level 4 WBS item for this work package
		const randomLevel4Item =
			constructionLevel4Items[Math.floor(Math.random() * constructionLevel4Items.length)];

		// Create work package first
		const { data: workPackage } = await supabase
			.from('work_package')
			.insert({
				name: faker.commerce.productName(),
				description: faker.commerce.productDescription(),
				project_id: projectId,
				wbs_library_item_id: randomLevel4Item.wbs_library_item_id,
			})
			.select('work_package_id')
			.single();
		if (!workPackage) throw new Error('Work package insert & select failed');

		// Create purchase order with work package reference
		const { data: po } = await supabase
			.from('purchase_order')
			.insert({
				po_number: faker.string.alphanumeric(8).toUpperCase(),
				po_date: faker.date.recent({ days: 180 }).toISOString(),
				vendor_id: vendorId,
				project_id: projectId,
				work_package_id: workPackage.work_package_id,
				description: faker.commerce.productDescription(),
				original_amount: faker.number.int({ min: 100000, max: 1000000 }),
			})
			.select('purchase_order_id')
			.single();
		if (!po) throw new Error('PO insert & select failed');
		poIds.push(po.purchase_order_id);

		while (Math.random() < 0.7) {
			await supabase.from('invoice').insert({
				purchase_order_id: po.purchase_order_id,
				description: faker.finance.transactionDescription(),
				invoice_date: faker.date.recent({ days: 15 }).toISOString(),
				account: '1000',
				amount: faker.number.int({ min: 50000, max: 500000 }),
				post_date: faker.date.recent({ days: 15 }).toISOString(),
			});
		}
	}

	// Risks and approved changes
	for (let i = 0; i < 10; i++) {
		const { data: risk } = await supabase
			.from('risk_register')
			.insert({
				project_id: projectId,
				title: faker.lorem.sentence(),
				description: faker.lorem.paragraph(),
				status: 'identified',
				probability: faker.number.int({ min: 10, max: 90 }),
				potential_impact: faker.number.int({ min: 10000, max: 200000 }),
				date_identified: faker.date.past().toISOString(),
			})
			.select('risk_id')
			.single();

		if (!risk) throw new Error('Risk insert & select failed');

		if (Math.random() < 0.3) {
			await supabase.from('approved_changes').insert({
				project_id: projectId,
				title: `Change: ${faker.lorem.words(3)}`,
				description: faker.lorem.paragraph(),
				status: 'approved',
				original_risk_id: risk.risk_id,
				date_identified: faker.date.past().toISOString(),
				date_approved: faker.date.recent().toISOString(),
				potential_impact: faker.number.int({ min: 10000, max: 100000 }),
			});
		}
	}

	return { project_id: projectId };
}

export default generateRibaDemoProjectData;
